import React from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { AlertTriangle } from 'lucide-react'

export const EmergencyAuthFix: React.FC = () => {
  const handleEmergencyFix = () => {
    try {
      // Clear all possible auth storage
      localStorage.clear()
      sessionStorage.clear()
      
      // Clear any Supabase specific storage
      const keys = Object.keys(localStorage)
      keys.forEach(key => {
        if (key.includes('supabase') || key.includes('auth')) {
          localStorage.removeItem(key)
        }
      })
      
      // Clear cookies (if possible)
      document.cookie.split(";").forEach(function(c) { 
        document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/"); 
      })
      
      // Force reload
      window.location.href = window.location.origin
    } catch (error) {
      console.error('Emergency fix error:', error)
      // Fallback - just reload
      window.location.reload()
    }
  }

  return (
    <div className="fixed top-4 right-4 z-50">
      <Button 
        onClick={handleEmergencyFix}
        variant="destructive"
        size="sm"
        className="bg-red-600 hover:bg-red-700"
      >
        <AlertTriangle className="h-4 w-4 mr-2" />
        Emergency Auth Fix
      </Button>
    </div>
  )
}
