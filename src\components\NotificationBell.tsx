import React from 'react'
import { <PERSON> } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { useAuth } from '@/contexts/AuthContext'
import { useNotifications } from '@/hooks/useNotifications'

interface NotificationBellProps {
  className?: string
  variant?: 'header' | 'sidebar'
}

export const NotificationBell: React.FC<NotificationBellProps> = ({
  className,
  variant = 'header'
}) => {
  const { user } = useAuth()
  const { unreadCount, loading } = useNotifications({ limit: 5 })

  // Only render if user is authenticated
  if (!user || loading) {
    return null
  }

  return (
    <Button
      variant="ghost"
      size="sm"
      className={`relative ${className}`}
    >
      <Bell className="h-5 w-5" />
      {unreadCount > 0 && (
        <Badge
          variant="destructive"
          className="absolute -top-2 -right-2 h-5 w-5 p-0 text-xs flex items-center justify-center"
        >
          {unreadCount > 9 ? '9+' : unreadCount}
        </Badge>
      )}
      <span className="sr-only">Notifications</span>
    </Button>
  )
}

export default NotificationBell