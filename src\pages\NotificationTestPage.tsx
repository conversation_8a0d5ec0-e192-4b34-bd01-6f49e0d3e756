import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { useAuth } from '@/contexts/AuthContext';
import { NotificationTestComponent } from '@/features/events/components/NotificationTestComponent';

const NotificationTestPage = () => {
  const { user } = useAuth();

  useEffect(() => {
    loadEvents();
  }, []);

  const loadEvents = async () => {
    try {
      setLoading(true);
      const { events } = await EventService.getEvents({}, { field: 'start_date', direction: 'desc' }, 1, 5);
      setEvents(events);
    } catch (error) {
      console.error('Error loading events:', error);
      toast({
        title: "Error",
        description: "Failed to load events",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const createTestEvent = async () => {
    if (!user) {
      toast({
        title: "Error",
        description: "You must be logged in to create a test event",
        variant: "destructive"
      });
      return;
    }

    try {
      setCreating(true);
      const testEvent = {
        name: `Test Event - ${new Date().toLocaleString()}`,
        description: 'This is a test event to demonstrate the notification system. When someone registers for this event, the organizer will receive a notification.',
        organizer_name: 'Test Organizer',
        organizer_email: user.email || '<EMAIL>',
        start_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // Next week
        start_time: '10:00',
        end_time: '12:00',
        location_type: 'online' as const,
        online_meeting_url: 'https://meet.google.com/test-meeting',
        max_attendees: 50,
        registration_required: true
      };

      await EventService.createEvent(testEvent);
      toast({
        title: "Success",
        description: "Test event created successfully!"
      });
      loadEvents();
    } catch (error) {
      console.error('Error creating test event:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to create test event",
        variant: "destructive"
      });
    } finally {
      setCreating(false);
    }
  };

  if (!user) {
    return (
      <div className="py-12 px-4 sm:px-6 lg:px-8 min-h-screen bg-background">
        <div className="max-w-4xl mx-auto">
          <Card>
            <CardContent className="py-12 text-center">
              <h3 className="text-lg font-semibold mb-2">Please log in</h3>
              <p className="text-muted-foreground">
                You need to be logged in to test the notification system.
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="py-12 px-4 sm:px-6 lg:px-8 min-h-screen bg-background">
      <div className="max-w-4xl mx-auto space-y-6">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-foreground mb-4">Notification System Test</h1>
          <p className="text-muted-foreground mb-8">
            This page helps test the end-to-end notification flow. Create a test event, then register for it with another account to see notifications in action.
          </p>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Step 1: Create a Test Event</CardTitle>
            <CardDescription>
              Create a test event to demonstrate the notification system. You'll be the organizer and will receive notifications when others register.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button 
              onClick={createTestEvent} 
              disabled={creating}
              className="w-full"
            >
              {creating ? 'Creating Test Event...' : 'Create Test Event'}
            </Button>
          </CardContent>
        </Card>

        {loading ? (
          <Card>
            <CardContent className="py-12 text-center">
              <div className="text-foreground">Loading events...</div>
            </CardContent>
          </Card>
        ) : (
          <div className="space-y-4">
            <h2 className="text-2xl font-semibold">Step 2: Register for Events</h2>
            <p className="text-muted-foreground mb-4">
              Register for the events below to test the notification system. The event organizer will receive a notification when you register.
            </p>
            
            {events.length === 0 ? (
              <Card>
                <CardContent className="py-12 text-center">
                  <p className="text-muted-foreground">
                    No events found. Create a test event above to get started.
                  </p>
                </CardContent>
              </Card>
            ) : (
              events.map((event) => (
                <Card key={event.id} className="space-y-4">
                  <CardHeader>
                    <div className="flex justify-between items-start">
                      <div>
                        <CardTitle className="text-lg">{event.name}</CardTitle>
                        <CardDescription>
                          Organized by {event.organizer_name} • {new Date(event.start_date).toLocaleDateString()}
                        </CardDescription>
                      </div>
                      {event.created_by_user_id === user.id && (
                        <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                          Your Event
                        </span>
                      )}
                    </div>
                  </CardHeader>
                  <CardContent>
                    {event.description && (
                      <p className="text-sm text-muted-foreground mb-4">{event.description}</p>
                    )}
                    
                    {event.created_by_user_id !== user.id ? (
                      <TestEventRegistration 
                        eventId={event.id} 
                        eventName={event.name}
                      />
                    ) : (
                      <div className="p-3 bg-blue-50 border border-blue-200 rounded-md">
                        <p className="text-sm text-blue-800">
                          ℹ️ This is your event. When others register for it, you'll receive a notification.
                        </p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))
            )}
          </div>
        )}

        <Card>
          <CardHeader>
            <CardTitle>Step 3: Check Notifications</CardTitle>
            <CardDescription>
              After registering for an event, check your notifications in the header bell icon or visit your profile dashboard.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <p className="text-sm">
                • Look for the notification bell icon in the header (it should show a count badge)
              </p>
              <p className="text-sm">
                • Click the bell to see recent notifications
              </p>
              <p className="text-sm">
                • Visit your profile → Notifications to see all notifications
              </p>
              <p className="text-sm">
                • Check the notification sidebar widget in your profile page
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default NotificationTestPage;
