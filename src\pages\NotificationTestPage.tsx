import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { useAuth } from '@/contexts/AuthContext';
import { NotificationTestComponent } from '@/features/events/components/NotificationTestComponent';

const NotificationTestPage = () => {
  const { user } = useAuth();

  if (!user) {
    return (
      <div className="py-12 px-4 sm:px-6 lg:px-8 min-h-screen bg-background">
        <div className="max-w-4xl mx-auto">
          <Card>
            <CardContent className="py-12 text-center">
              <h3 className="text-lg font-semibold mb-2">Please log in</h3>
              <p className="text-muted-foreground">
                You need to be logged in to test the notification system.
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <NotificationTestComponent />
    </div>
  );
};

export default NotificationTestPage;
