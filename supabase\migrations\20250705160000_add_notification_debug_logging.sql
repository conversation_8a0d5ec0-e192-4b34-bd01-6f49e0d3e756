-- Add debug logging to notification triggers to help troubleshoot
-- This will help us understand why notifications aren't being created

-- Create a debug log table for troubleshooting
CREATE TABLE IF NOT EXISTS public.notification_debug_log (
    id SERIAL PRIMARY KEY,
    trigger_name TEXT NOT NULL,
    event_id UUID,
    user_id UUID,
    organizer_id UUID,
    is_attending BOOLEAN,
    is_interested BOOLEAN,
    condition_met TEXT,
    notification_created BOOLEAN DEFAULT false,
    debug_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Grant permissions
GRANT ALL ON TABLE public.notification_debug_log TO authenticated;
GRANT ALL ON TABLE public.notification_debug_log TO service_role;
GRANT ALL ON SEQUENCE public.notification_debug_log_id_seq TO authenticated;
GRANT ALL ON SEQUENCE public.notification_debug_log_id_seq TO service_role;

-- Enhanced function to handle event registration notifications with debug logging
CREATE OR REPLACE FUNCTION notify_event_organizer_on_registration()
RETURNS TRIGGER AS $$
DECLARE
    event_record RECORD;
    organizer_id UUID;
    user_record RECORD;
    notification_title TEXT;
    notification_message TEXT;
    action_url TEXT;
    notification_data JSONB;
    debug_msg TEXT;
    notification_created BOOLEAN := false;
BEGIN
    -- Log the trigger execution
    INSERT INTO public.notification_debug_log (
        trigger_name, event_id, user_id, is_attending, is_interested, 
        condition_met, debug_message
    ) VALUES (
        'notify_event_organizer_on_registration',
        NEW.event_id,
        NEW.user_id,
        NEW.is_attending,
        NEW.is_interested,
        'trigger_started',
        'Trigger fired for event_id: ' || NEW.event_id || ', user_id: ' || NEW.user_id
    );

    -- Only process if user is attending (registering for the event)
    IF NEW.is_attending = true AND (OLD.is_attending IS NULL OR OLD.is_attending = false) THEN
        debug_msg := 'Condition met: is_attending = true';
        
        -- Get event details and organizer
        SELECT e.*, e.created_by_user_id
        INTO event_record
        FROM events e
        WHERE e.id = NEW.event_id;
        
        IF event_record.id IS NULL THEN
            debug_msg := debug_msg || '; Event not found';
        ELSE
            debug_msg := debug_msg || '; Event found: ' || event_record.name;
            organizer_id := event_record.created_by_user_id;
            
            -- Get user details
            SELECT p.first_name, p.last_name
            INTO user_record
            FROM profiles p
            WHERE p.id = NEW.user_id;
            
            -- Check if user is registering for their own event
            IF event_record.created_by_user_id = NEW.user_id THEN
                debug_msg := debug_msg || '; User registering for own event - notification skipped';
            ELSE
                debug_msg := debug_msg || '; Different user registering - creating notification';
                
                -- Construct notification
                notification_title := 'New Event Registration';
                notification_message := COALESCE(user_record.first_name || ' ' || user_record.last_name, 'A user') 
                    || ' has registered for your event "' || event_record.name || '"';
                
                action_url := '/events/' || NEW.event_id;
                
                notification_data := jsonb_build_object(
                    'event_id', NEW.event_id,
                    'event_name', event_record.name,
                    'user_id', NEW.user_id,
                    'user_name', COALESCE(user_record.first_name || ' ' || user_record.last_name, 'Anonymous User'),
                    'registration_type', 'attending'
                );
                
                -- Create notification for event organizer
                PERFORM create_notification(
                    event_record.created_by_user_id,
                    'event_registration'::notification_type_enum,
                    notification_title,
                    notification_message,
                    NEW.event_id,
                    'event',
                    action_url,
                    notification_data
                );
                
                notification_created := true;
                debug_msg := debug_msg || '; Notification created successfully';
            END IF;
        END IF;
    ELSE
        debug_msg := 'Condition not met: is_attending = ' || COALESCE(NEW.is_attending::text, 'NULL') || 
                    ', OLD.is_attending = ' || COALESCE(OLD.is_attending::text, 'NULL');
    END IF;
    
    -- Log the final result
    INSERT INTO public.notification_debug_log (
        trigger_name, event_id, user_id, organizer_id, is_attending, is_interested,
        condition_met, notification_created, debug_message
    ) VALUES (
        'notify_event_organizer_on_registration',
        NEW.event_id,
        NEW.user_id,
        organizer_id,
        NEW.is_attending,
        NEW.is_interested,
        'trigger_completed',
        notification_created,
        debug_msg
    );
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to view debug logs
CREATE OR REPLACE FUNCTION get_notification_debug_logs(limit_count INTEGER DEFAULT 20)
RETURNS TABLE (
    id INTEGER,
    trigger_name TEXT,
    event_id UUID,
    user_id UUID,
    organizer_id UUID,
    is_attending BOOLEAN,
    is_interested BOOLEAN,
    condition_met TEXT,
    notification_created BOOLEAN,
    debug_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        ndl.id,
        ndl.trigger_name,
        ndl.event_id,
        ndl.user_id,
        ndl.organizer_id,
        ndl.is_attending,
        ndl.is_interested,
        ndl.condition_met,
        ndl.notification_created,
        ndl.debug_message,
        ndl.created_at
    FROM public.notification_debug_log ndl
    ORDER BY ndl.created_at DESC
    LIMIT limit_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission on the debug function
GRANT EXECUTE ON FUNCTION get_notification_debug_logs(INTEGER) TO authenticated;
GRANT EXECUTE ON FUNCTION get_notification_debug_logs(INTEGER) TO service_role;

-- Add comment
COMMENT ON TABLE public.notification_debug_log IS 'Debug logging table for notification triggers to help troubleshoot issues';
COMMENT ON FUNCTION get_notification_debug_logs(INTEGER) IS 'Function to retrieve recent notification debug logs for troubleshooting';
