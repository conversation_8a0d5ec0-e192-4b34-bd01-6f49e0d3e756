import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Checkbox } from '@/components/ui/checkbox'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { useAuth } from '@/contexts/AuthContext'
import { useToast } from '@/hooks/use-toast'
import { EventService } from '../services/eventService'
import { NotificationService } from '@/services/notificationService'
import { EventInterestFormData, Event, NewEvent } from '../types'
import { Bell, Users, Calendar, MapPin, TestTube, CheckCircle } from 'lucide-react'

export const NotificationTestComponent: React.FC = () => {
  const [formData, setFormData] = useState<EventInterestFormData>({
    is_interested: false,
    is_attending: true, // Default to attending for registration
    gdpr_consent_given: true, // Default to true for testing
    visibility_consent_given: false,
    note: 'Test registration for notification system'
  })
  const [loading, setLoading] = useState(false)
  const [creatingEvent, setCreatingEvent] = useState(false)
  const [testEvent, setTestEvent] = useState<Event | null>(null)
  const [notifications, setNotifications] = useState<any[]>([])
  const [loadingNotifications, setLoadingNotifications] = useState(false)
  const [testStep, setTestStep] = useState<'create' | 'register' | 'complete'>('create')
  const { user } = useAuth()
  const { toast } = useToast()

  // Load recent notifications on component mount
  useEffect(() => {
    loadNotifications()
  }, [])

  const loadNotifications = async () => {
    if (!user) return
    
    try {
      setLoadingNotifications(true)
      const result = await NotificationService.getUserNotifications({ 
        type: ['event_registration'],
        limit: 10 
      })
      setNotifications(result.notifications)
    } catch (error) {
      console.error('Error loading notifications:', error)
    } finally {
      setLoadingNotifications(false)
    }
  }

  const createTestEvent = async () => {
    if (!user) {
      toast({
        title: "Error",
        description: "You must be logged in to create test events",
        variant: "destructive"
      })
      return
    }

    try {
      setCreatingEvent(true)
      
      const testEventData: NewEvent = {
        name: `Notification Test Event - ${new Date().toLocaleTimeString()}`,
        description: 'This is a test event created specifically for testing the notification system when users register for events.',
        organizer_name: `${user.user_metadata?.first_name || 'Test'} ${user.user_metadata?.last_name || 'User'}`,
        organizer_email: user.email,
        start_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 7 days from now
        start_time: '10:00',
        end_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        end_time: '12:00',
        location_type: 'online',
        online_meeting_url: 'https://zoom.us/test-meeting-notification',
        max_attendees: 50,
        registration_required: true,
        tags: ['test', 'notification-testing', 'automated-test']
      }

      const createdEvent = await EventService.createEvent(testEventData)
      setTestEvent(createdEvent)
      setTestStep('register')
      
      toast({
        title: "✅ Step 1 Complete",
        description: `Test event created: ${createdEvent.name}`,
      })
    } catch (error) {
      console.error('Error creating test event:', error)
      toast({
        title: "Error",
        description: "Failed to create test event",
        variant: "destructive"
      })
    } finally {
      setCreatingEvent(false)
    }
  }

  const handleRegistration = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!user || !testEvent) {
      toast({
        title: "Error",
        description: "You must be logged in and have a test event created",
        variant: "destructive"
      })
      return
    }

    try {
      setLoading(true)
      await EventService.expressInterest(testEvent.id, formData)
      
      setTestStep('complete')
      
      toast({
        title: "✅ Step 2 Complete",
        description: `Registration successful! Check notifications in a moment.`,
      })
      
      // Reload notifications after a short delay to see if new one appeared
      setTimeout(() => {
        loadNotifications()
        toast({
          title: "🔔 Check Notifications",
          description: "If the system is working, you should see a new notification below.",
        })
      }, 2000)
      
    } catch (error) {
      console.error('Error registering for event:', error)
      toast({
        title: "Registration Failed",
        description: error instanceof Error ? error.message : "Failed to register for event",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  const resetTest = () => {
    setTestEvent(null)
    setTestStep('create')
    setFormData({
      is_interested: false,
      is_attending: true,
      gdpr_consent_given: true,
      visibility_consent_given: false,
      note: 'Test registration for notification system'
    })
  }

  return (
    <div className="space-y-6 max-w-4xl mx-auto p-6">
      {/* Header */}
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold flex items-center justify-center gap-2">
          <TestTube className="h-8 w-8" />
          Event Registration Notification Test
        </h1>
        <p className="text-muted-foreground">
          Test the notification system by creating an event and registering for it
        </p>
      </div>

      {/* Test Progress */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle className="h-5 w-5" />
            Test Progress
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4">
            <div className={`flex items-center gap-2 ${testStep === 'create' ? 'text-blue-600' : testStep === 'register' || testStep === 'complete' ? 'text-green-600' : 'text-gray-400'}`}>
              <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${testStep === 'create' ? 'bg-blue-100 text-blue-600' : testStep === 'register' || testStep === 'complete' ? 'bg-green-100 text-green-600' : 'bg-gray-100 text-gray-400'}`}>
                1
              </div>
              <span>Create Test Event</span>
            </div>
            <div className="flex-1 h-px bg-gray-200"></div>
            <div className={`flex items-center gap-2 ${testStep === 'register' ? 'text-blue-600' : testStep === 'complete' ? 'text-green-600' : 'text-gray-400'}`}>
              <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${testStep === 'register' ? 'bg-blue-100 text-blue-600' : testStep === 'complete' ? 'bg-green-100 text-green-600' : 'bg-gray-100 text-gray-400'}`}>
                2
              </div>
              <span>Register for Event</span>
            </div>
            <div className="flex-1 h-px bg-gray-200"></div>
            <div className={`flex items-center gap-2 ${testStep === 'complete' ? 'text-green-600' : 'text-gray-400'}`}>
              <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${testStep === 'complete' ? 'bg-green-100 text-green-600' : 'bg-gray-100 text-gray-400'}`}>
                3
              </div>
              <span>Check Notifications</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Step 1: Create Test Event */}
      {testStep === 'create' && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              Step 1: Create Test Event
            </CardTitle>
            <CardDescription>
              First, create a test event that you can register for to trigger notifications
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <h4 className="font-medium text-blue-900">What this does:</h4>
              <ul className="text-sm text-blue-800 mt-2 space-y-1">
                <li>• Creates a test event with you as the organizer</li>
                <li>• Sets up the event for notification testing</li>
                <li>• Prepares the system to send you notifications when someone registers</li>
              </ul>
            </div>
            
            <Button 
              onClick={createTestEvent} 
              disabled={creatingEvent}
              className="w-full"
              size="lg"
            >
              {creatingEvent ? 'Creating Test Event...' : 'Create Test Event'}
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Step 2: Register for Event */}
      {testStep === 'register' && testEvent && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Step 2: Register for Your Event
            </CardTitle>
            <CardDescription>
              Now register for the event you just created to trigger the notification
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="p-4 bg-muted rounded-lg">
              <h4 className="font-medium flex items-center gap-2">
                <MapPin className="h-4 w-4" />
                {testEvent.name}
              </h4>
              <p className="text-sm text-muted-foreground mt-1">
                Test event for notification testing
              </p>
              <Badge variant="secondary" className="mt-2">
                Test Event
              </Badge>
            </div>

            <div className="p-4 bg-amber-50 border border-amber-200 rounded-lg">
              <h4 className="font-medium text-amber-900">What this does:</h4>
              <ul className="text-sm text-amber-800 mt-2 space-y-1">
                <li>• Registers you for the event (sets is_attending = true)</li>
                <li>• Triggers the database notification function</li>
                <li>• Should create a notification for the event organizer (you)</li>
              </ul>
            </div>

            <Separator />

            <form onSubmit={handleRegistration} className="space-y-4">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="is_attending"
                  checked={formData.is_attending}
                  onCheckedChange={(checked) => 
                    setFormData(prev => ({ ...prev, is_attending: checked as boolean }))
                  }
                />
                <Label htmlFor="is_attending">I plan to attend this event</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="gdpr_consent"
                  checked={formData.gdpr_consent_given}
                  onCheckedChange={(checked) => 
                    setFormData(prev => ({ ...prev, gdpr_consent_given: checked as boolean }))
                  }
                />
                <Label htmlFor="gdpr_consent">I consent to the organizer contacting me</Label>
              </div>

              <div>
                <Label htmlFor="note">Note (optional)</Label>
                <Textarea
                  id="note"
                  value={formData.note || ''}
                  onChange={(e) => setFormData(prev => ({ ...prev, note: e.target.value }))}
                  placeholder="Test registration for notification system"
                  rows={3}
                />
              </div>

              <Button 
                type="submit" 
                disabled={loading || !formData.is_attending}
                className="w-full"
                size="lg"
              >
                {loading ? 'Registering...' : 'Register for Event & Test Notifications'}
              </Button>
            </form>
          </CardContent>
        </Card>
      )}

      {/* Step 3: Check Results */}
      {testStep === 'complete' && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-600" />
              Step 3: Test Complete!
            </CardTitle>
            <CardDescription>
              Registration successful. Check the notifications below to see if the system worked.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
              <h4 className="font-medium text-green-900">✅ Test completed successfully!</h4>
              <p className="text-sm text-green-800 mt-1">
                You have registered for your test event. If the notification system is working correctly, 
                you should see a new notification in the section below.
              </p>
            </div>
            
            <Button 
              onClick={resetTest}
              variant="outline"
              className="w-full"
            >
              Run Test Again
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Notifications Display */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bell className="h-5 w-5" />
            Recent Event Registration Notifications
          </CardTitle>
          <CardDescription>
            Notifications you've received when users register for your events
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={loadNotifications}
                disabled={loadingNotifications}
              >
                {loadingNotifications ? 'Loading...' : 'Refresh Notifications'}
              </Button>

              {testStep === 'complete' && (
                <Badge variant="outline" className="text-green-600 border-green-600">
                  Look for new notifications here
                </Badge>
              )}
            </div>

            {notifications.length === 0 ? (
              <div className="text-center py-8">
                <Bell className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                <p className="text-sm text-muted-foreground">
                  No event registration notifications found yet.
                </p>
                <p className="text-xs text-muted-foreground mt-1">
                  Complete the test above to generate a notification.
                </p>
              </div>
            ) : (
              <div className="space-y-3">
                {notifications.map((notification) => (
                  <div key={notification.id} className="p-4 border rounded-lg bg-card">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <h5 className="font-medium text-sm">{notification.title}</h5>
                          <Badge variant={notification.status === 'unread' ? 'default' : 'secondary'}>
                            {notification.status}
                          </Badge>
                        </div>
                        <p className="text-sm text-muted-foreground mb-3">
                          {notification.message}
                        </p>
                        <div className="flex items-center gap-4 text-xs text-muted-foreground">
                          <span>
                            📅 {new Date(notification.created_at).toLocaleString()}
                          </span>
                          {notification.related_id && (
                            <span>
                              🎯 Event ID: {notification.related_id.slice(0, 8)}...
                            </span>
                          )}
                        </div>
                        {notification.data && Object.keys(notification.data).length > 0 && (
                          <div className="mt-2 p-2 bg-muted rounded text-xs">
                            <strong>Additional data:</strong>
                            <pre className="mt-1 text-xs">
                              {JSON.stringify(notification.data, null, 2)}
                            </pre>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Instructions */}
      <Card>
        <CardHeader>
          <CardTitle>How This Test Works</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid md:grid-cols-2 gap-4">
            <div className="space-y-3">
              <h4 className="font-medium">Database Triggers</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• When a user sets `is_attending = true` in event_interests</li>
                <li>• The `notify_event_organizer_on_registration()` function triggers</li>
                <li>• A notification is created for the event organizer</li>
                <li>• Real-time updates push the notification to the UI</li>
              </ul>
            </div>
            <div className="space-y-3">
              <h4 className="font-medium">What to Look For</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• New notification appears in the list above</li>
                <li>• Notification type is "event_registration"</li>
                <li>• Contains user and event details</li>
                <li>• Status is "unread" initially</li>
              </ul>
            </div>
          </div>

          <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <h4 className="font-medium text-blue-900">💡 Troubleshooting</h4>
            <ul className="text-sm text-blue-800 mt-2 space-y-1">
              <li>• If no notification appears, check the database triggers are working</li>
              <li>• Ensure you're the creator of the test event</li>
              <li>• Try refreshing notifications after a few seconds</li>
              <li>• Check browser console for any errors</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
