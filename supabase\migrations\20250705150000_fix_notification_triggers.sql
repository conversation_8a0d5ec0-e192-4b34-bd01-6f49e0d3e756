-- Fix notification trigger functions to remove email column reference
-- The profiles table doesn't have an email column - email is in auth.users

-- Function to handle event registration notifications (FIXED)
CREATE OR REPLACE FUNCTION notify_event_organizer_on_registration()
RETURNS TRIGGER AS $$
DECLARE
    event_record RECORD;
    organizer_id UUID;
    user_record RECORD;
    notification_title TEXT;
    notification_message TEXT;
    action_url TEXT;
    notification_data JSONB;
BEGIN
    -- Only process if user is attending (registering for the event)
    IF NEW.is_attending = true AND (OLD.is_attending IS NULL OR OLD.is_attending = false) THEN
        -- Get event details and organizer
        SELECT e.*, e.created_by_user_id
        INTO event_record
        FROM events e
        WHERE e.id = NEW.event_id;
        
        -- Get user details (removed email reference)
        SELECT p.first_name, p.last_name
        INTO user_record
        FROM profiles p
        WHERE p.id = NEW.user_id;
        
        -- Don't notify if user is registering for their own event
        IF event_record.created_by_user_id != NEW.user_id THEN
            -- Construct notification
            notification_title := 'New Event Registration';
            notification_message := COALESCE(user_record.first_name || ' ' || user_record.last_name, 'A user') 
                || ' has registered for your event "' || event_record.name || '"';
            
            action_url := '/events/' || NEW.event_id;
            
            notification_data := jsonb_build_object(
                'event_id', NEW.event_id,
                'event_name', event_record.name,
                'user_id', NEW.user_id,
                'user_name', COALESCE(user_record.first_name || ' ' || user_record.last_name, 'Anonymous User'),
                'registration_type', 'attending'
            );
            
            -- Create notification for event organizer
            PERFORM create_notification(
                event_record.created_by_user_id,
                'event_registration'::notification_type_enum,
                notification_title,
                notification_message,
                NEW.event_id,
                'event',
                action_url,
                notification_data
            );
        END IF;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to handle event interest notifications (FIXED)
CREATE OR REPLACE FUNCTION notify_event_organizer_on_interest()
RETURNS TRIGGER AS $$
DECLARE
    event_record RECORD;
    organizer_id UUID;
    user_record RECORD;
    notification_title TEXT;
    notification_message TEXT;
    action_url TEXT;
    notification_data JSONB;
BEGIN
    -- Only process if user is expressing interest (not registering)
    IF NEW.is_interested = true AND NEW.is_attending = false AND 
       (OLD.is_interested IS NULL OR OLD.is_interested = false) THEN
        
        -- Get event details and organizer
        SELECT e.*, e.created_by_user_id
        INTO event_record
        FROM events e
        WHERE e.id = NEW.event_id;
        
        -- Get user details (removed email reference)
        SELECT p.first_name, p.last_name
        INTO user_record
        FROM profiles p
        WHERE p.id = NEW.user_id;
        
        -- Don't notify if user is expressing interest in their own event
        IF event_record.created_by_user_id != NEW.user_id THEN
            -- Construct notification
            notification_title := 'New Event Interest';
            notification_message := COALESCE(user_record.first_name || ' ' || user_record.last_name, 'A user') 
                || ' has expressed interest in your event "' || event_record.name || '"';
            
            action_url := '/events/' || NEW.event_id;
            
            notification_data := jsonb_build_object(
                'event_id', NEW.event_id,
                'event_name', event_record.name,
                'user_id', NEW.user_id,
                'user_name', COALESCE(user_record.first_name || ' ' || user_record.last_name, 'Anonymous User'),
                'registration_type', 'interested'
            );
            
            -- Create notification for event organizer
            PERFORM create_notification(
                event_record.created_by_user_id,
                'event_interest'::notification_type_enum,
                notification_title,
                notification_message,
                NEW.event_id,
                'event',
                action_url,
                notification_data
            );
        END IF;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- The triggers are already created, so we don't need to recreate them
-- They will automatically use the updated functions

-- Add a comment explaining the fix
COMMENT ON FUNCTION notify_event_organizer_on_registration() IS 'Fixed function that creates notifications when users register for events - removed email column reference';
COMMENT ON FUNCTION notify_event_organizer_on_interest() IS 'Fixed function that creates notifications when users express interest in events - removed email column reference';
