import { supabase } from '@/integrations/supabase/client'

// Types for notifications
export type NotificationType = 'event_registration' | 'event_interest' | 'event_update' | 'event_reminder' | 'general'
export type NotificationStatus = 'unread' | 'read' | 'archived'

export interface Notification {
  id: string
  user_id: string
  type: NotificationType
  title: string
  message: string
  related_id?: string
  related_type?: string
  action_url?: string
  status: NotificationStatus
  created_at: string
  read_at?: string
  data?: Record<string, any>
}

export interface CreateNotificationData {
  user_id: string
  type: NotificationType
  title: string
  message: string
  related_id?: string
  related_type?: string
  action_url?: string
  data?: Record<string, any>
}

export interface NotificationFilters {
  status?: NotificationStatus[]
  type?: NotificationType[]
  limit?: number
  offset?: number
}

export class NotificationService {
  /**
   * Get notifications for the current user
   */
  static async getUserNotifications(filters: NotificationFilters = {}): Promise<{
    notifications: Notification[]
    count: number
  }> {
    try {
      const { data: { user }, error: authError } = await supabase.auth.getUser()

      if (authError) {
        console.warn('Auth error in getUserNotifications:', authError)
        return { notifications: [], count: 0 }
      }

      if (!user) {
        // Return empty result instead of throwing error to prevent loops
        return { notifications: [], count: 0 }
      }

      let query = supabase
        .from('notifications')
        .select('*', { count: 'exact' })
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })

      // Apply filters
      if (filters.status && filters.status.length > 0) {
        query = query.in('status', filters.status)
      }

      if (filters.type && filters.type.length > 0) {
        query = query.in('type', filters.type)
      }

      if (filters.limit) {
        query = query.limit(filters.limit)
      }

      if (filters.offset) {
        query = query.range(filters.offset, filters.offset + (filters.limit || 20) - 1)
      }

      const { data, error, count } = await query

      if (error) {
        console.error('Database error in getUserNotifications:', error)
        return { notifications: [], count: 0 }
      }

      return {
        notifications: data || [],
        count: count || 0
      }
    } catch (error) {
      console.error('Unexpected error in getUserNotifications:', error)
      return { notifications: [], count: 0 }
    }
  }

  /**
   * Get unread notification count for the current user
   */
  static async getUnreadCount(): Promise<number> {
    try {
      const { data: { user }, error: authError } = await supabase.auth.getUser()

      if (authError || !user) {
        return 0
      }

      const { data, error } = await supabase.rpc('get_unread_notification_count')

      if (error) {
        console.error('Error fetching unread count:', error)
        return 0
      }

      return data || 0
    } catch (error) {
      console.error('Unexpected error in getUnreadCount:', error)
      return 0
    }
  }

  /**
   * Mark a notification as read
   */
  static async markAsRead(notificationId: string): Promise<boolean> {
    const { data, error } = await supabase.rpc('mark_notification_read', {
      notification_id: notificationId
    })

    if (error) {
      throw new Error(`Failed to mark notification as read: ${error.message}`)
    }

    return data || false
  }

  /**
   * Mark all notifications as read for the current user
   */
  static async markAllAsRead(): Promise<number> {
    const { data, error } = await supabase.rpc('mark_all_notifications_read')

    if (error) {
      throw new Error(`Failed to mark all notifications as read: ${error.message}`)
    }

    return data || 0
  }

  /**
   * Update notification status
   */
  static async updateStatus(notificationId: string, status: NotificationStatus): Promise<Notification> {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      throw new Error('User must be authenticated to update notifications')
    }

    const updateData: any = { status }
    if (status === 'read') {
      updateData.read_at = new Date().toISOString()
    }

    const { data, error } = await supabase
      .from('notifications')
      .update(updateData)
      .eq('id', notificationId)
      .eq('user_id', user.id)
      .select()
      .single()

    if (error) {
      throw new Error(`Failed to update notification: ${error.message}`)
    }

    return data
  }

  /**
   * Delete a notification
   */
  static async deleteNotification(notificationId: string): Promise<void> {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      throw new Error('User must be authenticated to delete notifications')
    }

    const { error } = await supabase
      .from('notifications')
      .delete()
      .eq('id', notificationId)
      .eq('user_id', user.id)

    if (error) {
      throw new Error(`Failed to delete notification: ${error.message}`)
    }
  }

  /**
   * Create a notification (typically used by system functions)
   */
  static async createNotification(data: CreateNotificationData): Promise<Notification> {
    const { data: notification, error } = await supabase
      .from('notifications')
      .insert({
        user_id: data.user_id,
        type: data.type,
        title: data.title,
        message: data.message,
        related_id: data.related_id,
        related_type: data.related_type,
        action_url: data.action_url,
        data: data.data || {}
      })
      .select()
      .single()

    if (error) {
      throw new Error(`Failed to create notification: ${error.message}`)
    }

    return notification
  }

  /**
   * Subscribe to real-time notification updates for the current user
   */
  static async subscribeToNotifications(
    onNotification: (notification: Notification) => void,
    onError?: (error: any) => void
  ): Promise<() => void> {
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    
    if (userError) {
      throw new Error(`Failed to get authenticated user: ${userError.message}`)
    }
    
    if (!user) {
      throw new Error('User must be authenticated to subscribe to notifications')
    }

    const subscription = supabase
      .channel('notifications')
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'notifications',
          filter: `user_id=eq.${user.id}`
        },
        (payload) => {
          onNotification(payload.new as Notification)
        }
      )
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'notifications',
          filter: `user_id=eq.${user.id}`
        },
        (payload) => {
          onNotification(payload.new as Notification)
        }
      )
      .subscribe((status) => {
        if (status === 'SUBSCRIBED') {
          console.log('Successfully subscribed to notifications')
        } else if (status === 'CHANNEL_ERROR') {
          console.error('Failed to subscribe to notifications')
          onError?.(status)
        }
      })

    return () => {
      subscription.unsubscribe()
    }
  }

  /**
   * Helper method to format notification time
   */
  static formatNotificationTime(timestamp: string): string {
    const date = new Date(timestamp)
    const now = new Date()
    const diffInMs = now.getTime() - date.getTime()
    const diffInMinutes = Math.floor(diffInMs / (1000 * 60))
    const diffInHours = Math.floor(diffInMinutes / 60)
    const diffInDays = Math.floor(diffInHours / 24)

    if (diffInMinutes < 1) {
      return 'Just now'
    } else if (diffInMinutes < 60) {
      return `${diffInMinutes}m ago`
    } else if (diffInHours < 24) {
      return `${diffInHours}h ago`
    } else if (diffInDays < 7) {
      return `${diffInDays}d ago`
    } else {
      return date.toLocaleDateString()
    }
  }

  /**
   * Get notification icon based on type
   */
  static getNotificationIcon(type: NotificationType): string {
    switch (type) {
      case 'event_registration':
        return '✅'
      case 'event_interest':
        return '💝'
      case 'event_update':
        return '📝'
      case 'event_reminder':
        return '⏰'
      case 'general':
      default:
        return '📢'
    }
  }

  /**
   * Get notification color based on type
   */
  static getNotificationColor(type: NotificationType): string {
    switch (type) {
      case 'event_registration':
        return 'text-green-600'
      case 'event_interest':
        return 'text-pink-600'
      case 'event_update':
        return 'text-blue-600'
      case 'event_reminder':
        return 'text-orange-600'
      case 'general':
      default:
        return 'text-gray-600'
    }
  }
}
