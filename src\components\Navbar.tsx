import { useAuth } from "@/contexts/AuthContext";
import { But<PERSON> } from "@/components/ui/button";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { LogOut, User, Home, Users, Building2, Calendar, PoundSterling, Briefcase, ChevronDown, Menu, X, MessageCircle, GraduationCap } from "lucide-react";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import AvatarDisplay from "@/features/profile/components/AvatarDisplay";
import NotificationBell from "@/components/NotificationBell";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuLabel,
} from "@/components/ui/dropdown-menu";
import { useIsMobile } from "@/hooks/use-mobile";
import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";

const Navbar = () => {
  const { user, signOut } = useAuth();
  const location = useLocation();
  const navigate = useNavigate();
  const isMobile = useIsMobile();
  const [showFeatureMenu, setShowFeatureMenu] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [userProfile, setUserProfile] = useState<{
    first_name: string | null;
    last_name: string | null;
    avatar_url: string | null;
  } | null>(null);

  // Fetch user profile data for avatar
  useEffect(() => {
    const fetchUserProfile = async () => {
      if (!user) return;
      
      const { data, error } = await supabase
        .from('profiles')
        .select('first_name, last_name, avatar_url')
        .eq('id', user.id)
        .single();
      
      if (data && !error) {
        setUserProfile(data);
      } else if (error) {
        console.error('Error fetching user profile for navbar:', error);
      }
    };
    
    fetchUserProfile();
  }, [user]);

  const handleLogout = async () => {
    try {
      await signOut();
      setIsMobileMenuOpen(false);
      navigate('/');
    } catch (error) {
      console.error("Logout error:", error);
    }
  };

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const closeMobileMenu = () => {
    setIsMobileMenuOpen(false);
  };

  const isActive = (path: string) => {
    return location.pathname === path;
  };

  const getUserInitials = (email: string) => {
    if (!email) return "U";
    const parts = email.split("@")[0].split(".");
    if (parts.length >= 2) {
      return (parts[0][0] + parts[1][0]).toUpperCase();
    }
    return email.substring(0, 2).toUpperCase();
  };

  const featureLinks = [
    { path: '/events', name: 'Events', icon: <Calendar className="w-4 h-4 mr-2" />, disabled: false },
    { path: '/funding', name: 'Funding Finder', icon: <PoundSterling className="w-4 h-4 mr-2" />, disabled: false },
    { path: '/jobs', name: 'Jobs', icon: <Briefcase className="w-4 h-4 mr-2" />, disabled: false },
    { path: '/education', name: 'Education & Training', icon: <GraduationCap className="w-4 h-4 mr-2" />, disabled: false },
    { path: '/social', name: 'Social', icon: <MessageCircle className="w-4 h-4 mr-2" />, disabled: false }
  ];

  return (
    <nav className="bg-card shadow-sm border-b border-border sticky top-0 z-50">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* Logo/Brand */}
          <Link to="/" className="flex items-center space-x-2">
            <img src="/favicon.png" alt="NeXzero logo" className="w-8 h-8 rounded-lg" />
            <span className="text-foreground font-semibold text-lg select-none">NeXzero</span>
          </Link>

          {/* Navigation Links */}
          <div className="hidden md:flex items-center space-x-4">
            <Link to="/">
              <Button
                variant={isActive("/") ? "default" : "ghost"}
                size="sm"
                className={isActive("/") ? "bg-primary hover:bg-primary/90" : "text-muted-foreground hover:text-foreground"}
              >
                <Home className="w-4 h-4 mr-2" />
                Home
              </Button>
            </Link>
            
            {user && (
              <>
                <Link to="/members">
                  <Button
                    variant={isActive("/members") ? "default" : "ghost"}
                    size="sm"
                    className={isActive("/members") ? "bg-primary hover:bg-primary/90" : "text-muted-foreground hover:text-foreground"}
                  >
                    <Users className="w-4 h-4 mr-2" />
                    Members
                  </Button>
                </Link>
                
                <Link to="/business-directory">
                  <Button
                    variant={isActive("/business-directory") ? "default" : "ghost"}
                    size="sm"
                    className={isActive("/business-directory") ? "bg-primary hover:bg-primary/90" : "text-muted-foreground hover:text-foreground"}
                  >
                    <Building2 className="w-4 h-4 mr-2" />
                    Business Directory
                  </Button>
                </Link>
                
                <Link to="/events">
                  <Button
                    variant={isActive("/events") ? "default" : "ghost"}
                    size="sm"
                    className={isActive("/events") ? "bg-primary hover:bg-primary/90" : "text-muted-foreground hover:text-foreground"}
                  >
                    <Calendar className="w-4 h-4 mr-2" />
                    Events
                  </Button>
                </Link>

                <Link to="/funding">
                  <Button
                    variant={isActive("/funding") ? "default" : "ghost"}
                    size="sm"
                    className={isActive("/funding") ? "bg-primary hover:bg-primary/90" : "text-muted-foreground hover:text-foreground"}
                  >
                    <PoundSterling className="w-4 h-4 mr-2" />
                    Funding
                  </Button>
                </Link>

                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-muted-foreground hover:text-foreground"
                    >
                      More Features
                      <ChevronDown className="w-4 h-4 ml-1" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent className="bg-card border-border">
                    <DropdownMenuLabel>Platform Features</DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    {featureLinks.filter(link => link.path !== '/funding' && link.path !== '/events').map((link, index) => (
                      <DropdownMenuItem key={index} asChild>
                        <Link to={link.path} className="flex items-center text-muted-foreground hover:text-foreground cursor-pointer">
                          {link.icon}
                          {link.name}
                        </Link>
                      </DropdownMenuItem>
                    ))}
                  </DropdownMenuContent>
                </DropdownMenu>
                
                <Link to="/profile">
                  <Button
                    variant={isActive("/profile") ? "default" : "ghost"}
                    size="sm"
                    className={isActive("/profile") ? "bg-primary hover:bg-primary/90" : "text-muted-foreground hover:text-foreground"}
                  >
                    <User className="w-4 h-4 mr-2" />
                    Profile
                  </Button>
                </Link>
                

              </>
            )}
          </div>

          {/* Auth Actions */}
          <div className="flex items-center space-x-2">
            {user ? (
              <>
                {/* Mobile Menu Button */}
                <Button
                  variant="ghost"
                  size="sm"
                  className="md:hidden p-2"
                  onClick={toggleMobileMenu}
                  aria-label="Toggle mobile menu"
                >
                  {isMobileMenuOpen ? (
                    <X className="h-5 w-5" />
                  ) : (
                    <Menu className="h-5 w-5" />
                  )}
                </Button>

                {/* Notifications */}
                <NotificationBell variant="header" />

                {/* User Avatar Dropdown */}
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="relative h-8 w-8 rounded-full p-0">
                      <AvatarDisplay
                        avatarUrl={userProfile?.avatar_url}
                        firstName={userProfile?.first_name}
                        lastName={userProfile?.last_name}
                        email={user.email}
                        size="sm"
                      />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent className="w-56 bg-card border-border" align="end" forceMount>
                    <DropdownMenuItem className="text-muted-foreground hover:text-foreground hover:bg-accent" asChild>
                      <Link to="/profile" className="flex items-center" onClick={closeMobileMenu}>
                        <User className="mr-2 h-4 w-4" />
                        <span>Profile</span>
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem 
                      className="text-muted-foreground hover:text-foreground hover:bg-accent cursor-pointer"
                      onClick={handleLogout}
                    >
                      <LogOut className="mr-2 h-4 w-4" />
                      <span>Logout</span>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </>
            ) : (
              <Link to="/auth">
                <Button size="sm" className="bg-primary hover:bg-primary/90 text-primary-foreground">
                  Sign In
                </Button>
              </Link>
            )}
          </div>
        </div>

        {/* Mobile Menu Overlay */}
        {isMobileMenuOpen && user && (
          <div className="md:hidden border-t border-border bg-card">
            <div className="px-4 py-3 space-y-2">
              <Link to="/members" onClick={closeMobileMenu}>
                <Button
                  variant={isActive("/members") ? "default" : "ghost"}
                  size="sm"
                  className={`w-full justify-start ${isActive("/members") ? "bg-primary hover:bg-primary/90" : "text-muted-foreground hover:text-foreground"}`}
                >
                  <Users className="w-4 h-4 mr-2" />
                  Members
                </Button>
              </Link>
              
              <Link to="/business-directory" onClick={closeMobileMenu}>
                <Button
                  variant={isActive("/business-directory") ? "default" : "ghost"}
                  size="sm"
                  className={`w-full justify-start ${isActive("/business-directory") ? "bg-primary hover:bg-primary/90" : "text-muted-foreground hover:text-foreground"}`}
                >
                  <Building2 className="w-4 h-4 mr-2" />
                  Business Directory
                </Button>
              </Link>

              <Link to="/events" onClick={closeMobileMenu}>
                <Button
                  variant={isActive("/events") ? "default" : "ghost"}
                  size="sm"
                  className={`w-full justify-start ${isActive("/events") ? "bg-primary hover:bg-primary/90" : "text-muted-foreground hover:text-foreground"}`}
                >
                  <Calendar className="w-4 h-4 mr-2" />
                  Events
                </Button>
              </Link>

              <div className="space-y-1">
                <div className="text-sm font-medium text-muted-foreground px-3 py-2">More Features</div>
                {featureLinks.map((link, index) => (
                  <Link key={index} to={link.path} onClick={closeMobileMenu}>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="w-full justify-start text-muted-foreground hover:text-foreground"
                    >
                      {link.icon}
                      {link.name}
                    </Button>
                  </Link>
                ))}
              </div>
              
              <Link to="/profile" onClick={closeMobileMenu}>
                <Button
                  variant={isActive("/profile") ? "default" : "ghost"}
                  size="sm"
                  className={`w-full justify-start ${isActive("/profile") ? "bg-primary hover:bg-primary/90" : "text-muted-foreground hover:text-foreground"}`}
                >
                  <User className="w-4 h-4 mr-2" />
                  Profile
                </Button>
              </Link>
              

            </div>
          </div>
        )}
      </div>
    </nav>
  );
};

export default Navbar;
