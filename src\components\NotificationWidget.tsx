import React from 'react'
import { useNavigate } from 'react-router-dom'
import { Bell, ChevronRight } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { ScrollArea } from '@/components/ui/scroll-area'
import { useNotifications } from '@/hooks/useNotifications'
import { NotificationService, Notification } from '@/services/notificationService'
import { cn } from '@/lib/utils'

interface NotificationWidgetProps {
  className?: string
}

export const NotificationWidget: React.FC<NotificationWidgetProps> = ({ className }) => {
  const navigate = useNavigate()
  const { notifications, unreadCount, loading, markAsRead } = useNotifications({ 
    status: ['unread'], 
    limit: 5 
  })

  const handleNotificationClick = async (notification: Notification) => {
    try {
      // Mark as read if unread
      if (notification.status === 'unread') {
        await markAsRead(notification.id)
      }

      // Navigate to action URL if provided
      if (notification.action_url) {
        navigate(notification.action_url)
      }
    } catch (error) {
      console.error('Error handling notification click:', error)
    }
  }

  const handleViewAll = () => {
    navigate('/profile?tab=notifications')
  }

  if (loading) {
    return (
      <Card className={className}>
        <CardHeader className="pb-3">
          <CardTitle className="text-lg flex items-center gap-2">
            <Bell className="h-5 w-5" />
            Recent Notifications
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="animate-pulse">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-full"></div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg flex items-center gap-2">
            <Bell className="h-5 w-5" />
            Recent Notifications
          </CardTitle>
          {unreadCount > 0 && (
            <Badge variant="destructive" className="h-5 px-2">
              {unreadCount > 99 ? '99+' : unreadCount}
            </Badge>
          )}
        </div>
        <CardDescription>
          Stay updated with your latest activities
        </CardDescription>
      </CardHeader>
      <CardContent>
        {notifications.length === 0 ? (
          <div className="text-center py-6">
            <Bell className="h-8 w-8 text-gray-300 mx-auto mb-2" />
            <p className="text-sm text-gray-500">No recent notifications</p>
          </div>
        ) : (
          <div className="space-y-1">
            {notifications.map((notification) => (
              <div
                key={notification.id}
                className={cn(
                  "p-3 rounded-md border cursor-pointer hover:bg-gray-50 transition-colors",
                  notification.status === 'unread' && "bg-blue-50 border-blue-200"
                )}
                onClick={() => handleNotificationClick(notification)}
              >
                <div className="flex items-start justify-between">
                  <div className="flex items-start gap-2 flex-1 min-w-0">
                    <span className="text-sm mt-0.5" role="img" aria-label={notification.type}>
                      {NotificationService.getNotificationIcon(notification.type)}
                    </span>
                    <div className="flex-1 min-w-0">
                      <p className={cn(
                        "text-sm text-gray-900 truncate",
                        notification.status === 'unread' && "font-semibold"
                      )}>
                        {notification.title}
                      </p>
                      <p className="text-xs text-gray-500 mt-1 line-clamp-2">
                        {notification.message}
                      </p>
                      <p className="text-xs text-gray-400 mt-1">
                        {NotificationService.formatNotificationTime(notification.created_at)}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-1 ml-2">
                    {notification.status === 'unread' && (
                      <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    )}
                    <ChevronRight className="h-3 w-3 text-gray-400" />
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
        
        <div className="mt-4 pt-3 border-t">
          <Button
            variant="outline"
            size="sm"
            onClick={handleViewAll}
            className="w-full"
          >
            View all notifications
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}

export default NotificationWidget
