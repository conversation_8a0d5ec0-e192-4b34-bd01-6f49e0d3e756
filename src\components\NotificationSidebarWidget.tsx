import React from 'react'
import { Bell, Clock, CheckCircle, AlertCircle } from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { ScrollArea } from '@/components/ui/scroll-area'
import { useNavigate } from 'react-router-dom'
import { useNotifications } from '@/hooks/useNotifications'
import { NotificationService } from '@/services/notificationService'
import { cn } from '@/lib/utils'

interface NotificationWidgetProps {
  className?: string
  maxItems?: number
}

export const NotificationSidebarWidget: React.FC<NotificationWidgetProps> = ({ 
  className,
  maxItems = 5 
}) => {
  const navigate = useNavigate()
  const { notifications, unreadCount, loading } = useNotifications({ 
    limit: maxItems,
    status: ['unread'] // Only show unread notifications in widget
  })

  const handleNotificationClick = (notificationId: string, actionUrl?: string) => {
    if (actionUrl) {
      navigate(actionUrl)
    }
  }

  const handleViewAll = () => {
    navigate('/profile?tab=notifications')
  }

  if (loading) {
    return (
      <Card className={cn("w-full", className)}>
        <CardHeader className="pb-3">
          <CardTitle className="text-lg flex items-center gap-2">
            <Bell className="h-5 w-5" />
            Notifications
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="animate-pulse">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-full"></div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg flex items-center gap-2">
            <Bell className="h-5 w-5" />
            Recent Notifications
          </CardTitle>
          {unreadCount > 0 && (
            <Badge variant="destructive" className="h-5 px-2">
              {unreadCount > 99 ? '99+' : unreadCount}
            </Badge>
          )}
        </div>
      </CardHeader>
      <CardContent>
        {notifications.length === 0 ? (
          <div className="text-center py-6">
            <Bell className="h-8 w-8 text-gray-300 mx-auto mb-2" />
            <p className="text-sm text-gray-500">No new notifications</p>
          </div>
        ) : (
          <div className="space-y-2">
            <ScrollArea className="max-h-64">
              {notifications.slice(0, maxItems).map((notification) => (
                <div
                  key={notification.id}
                  className={cn(
                    "p-3 rounded-lg border cursor-pointer hover:bg-gray-50 transition-colors mb-2",
                    notification.status === 'unread' && "bg-blue-50 border-blue-200"
                  )}
                  onClick={() => handleNotificationClick(notification.id, notification.action_url)}
                >
                  <div className="flex items-start gap-3">
                    <div className="flex-shrink-0 mt-0.5">
                      <span className="text-sm" role="img" aria-label={notification.type}>
                        {NotificationService.getNotificationIcon(notification.type)}
                      </span>
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <p className={cn(
                          "text-sm font-medium text-gray-900 truncate",
                          notification.status === 'unread' && "font-semibold"
                        )}>
                          {notification.title}
                        </p>
                        {notification.status === 'unread' && (
                          <div className="w-2 h-2 bg-blue-500 rounded-full flex-shrink-0"></div>
                        )}
                      </div>
                      
                      <p className="text-xs text-gray-600 line-clamp-2 mb-1">
                        {notification.message}
                      </p>
                      
                      <p className="text-xs text-gray-400">
                        {NotificationService.formatNotificationTime(notification.created_at)}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </ScrollArea>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={handleViewAll}
              className="w-full mt-2"
            >
              View all notifications
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

export default NotificationSidebarWidget
