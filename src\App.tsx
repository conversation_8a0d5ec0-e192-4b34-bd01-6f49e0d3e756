import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, useLocation } from "react-router-dom";
import { useEffect } from "react";
import { AuthProvider } from "@/contexts/AuthContext";
import Layout from "@/components/Layout";
import { CookieConsentBanner } from "@/components/ui/cookie-consent";
import { Analytics } from "@vercel/analytics/react";

// Feature imports
import { HomePage } from "@/features/home";
import { AuthPage } from "@/features/auth";
import { ProfilePage } from "@/features/profile";
import { MembersPage, MemberDetailPage } from "@/features/members";
import { BusinessDirectoryPage, BusinessDetailPage, BusinessManagementPage } from "@/features/businessDirectory";
import { EventsDirectoryPage, EventDetailPage, EventManagementPage } from "@/features/events";
import { FundingFinderPage, FundingDetailPage } from "@/features/fundingFinder";
import { JobsPage } from "@/features/jobs";

// Shared pages
import NotFoundPage from "./pages/NotFoundPage";
import TestIndex from "./pages/testindex";
import NotificationTestPage from "./pages/NotificationTestPage";
import SocialComingSoonPage from "./pages/SocialComingSoonPage";
import ComingSoonPage from "./pages/ComingSoonPage";
import PrivacyPolicyPage from "./pages/PrivacyPolicyPage";
import TermsOfServicePage from "./pages/TermsOfServicePage";
import CookieSettingsPage from "./pages/CookieSettingsPage";

const queryClient = new QueryClient();

// Scroll to top component
const ScrollToTop = () => {
  const { pathname } = useLocation();

  useEffect(() => {
    window.scrollTo(0, 0);
  }, [pathname]);

  return null;
};

const App = () => (
  <QueryClientProvider client={queryClient}>
    <AuthProvider>
      <TooltipProvider>
        <Toaster />
        <Sonner />
        <BrowserRouter>
          <ScrollToTop />
          <Layout>
            <Routes>
              {/* Core Routes */}
              <Route path="/" element={<HomePage />} />
              <Route path="/auth" element={<AuthPage />} />
              <Route path="/profile" element={<ProfilePage />} />
              <Route path="/members" element={<MembersPage />} />
              <Route path="/members/:memberId" element={<MemberDetailPage />} />
              
              {/* Business Directory Routes */}
              <Route path="/business-directory" element={<BusinessDirectoryPage />} />
              <Route path="/business/:businessId" element={<BusinessDetailPage />} />
              <Route path="/business-management" element={<BusinessManagementPage />} />
              
              {/* Test Routes */}
              <Route path="/test" element={<TestIndex />} />
              <Route path="/test-notifications" element={<NotificationTestPage />} />
              
              {/* Feature Routes */}
              <Route path="/funding" element={<FundingFinderPage />} />
              <Route path="/funding/:fundingId" element={<FundingDetailPage />} />

              {/* Events Routes */}
              <Route path="/events" element={<EventsDirectoryPage />} />
              <Route path="/events/:eventId" element={<EventDetailPage />} />
              <Route path="/event-management" element={<EventManagementPage />} />

              {/* Legal Pages */}
              <Route path="/privacy-policy" element={<PrivacyPolicyPage />} />
              <Route path="/terms-of-service" element={<TermsOfServicePage />} />
              <Route path="/cookie-settings" element={<CookieSettingsPage />} />

              {/* Coming Soon Routes */}
              <Route path="/social" element={<SocialComingSoonPage />} />
              <Route path="/education" element={<ComingSoonPage />} />
              <Route path="/jobs" element={<ComingSoonPage />} />
              
              {/* Catch-all Route */}
              <Route path="*" element={<NotFoundPage />} />
            </Routes>
          </Layout>
          <CookieConsentBanner />
          <Analytics />
        </BrowserRouter>
      </TooltipProvider>
    </AuthProvider>
  </QueryClientProvider>
);

export default App;
