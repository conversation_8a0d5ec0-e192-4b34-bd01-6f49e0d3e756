import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { supabase } from '@/integrations/supabase/client'
import { User, Session } from '@supabase/supabase-js'
import { AlertTriangle, CheckCircle, XCircle } from 'lucide-react'

const AuthTestPage: React.FC = () => {
  const [user, setUser] = useState<User | null>(null)
  const [session, setSession] = useState<Session | null>(null)
  const [loading, setLoading] = useState(true)
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [authLogs, setAuthLogs] = useState<string[]>([])
  const [testResults, setTestResults] = useState<Record<string, boolean>>({})

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString()
    setAuthLogs(prev => [`[${timestamp}] ${message}`, ...prev.slice(0, 9)])
  }

  const runTest = async (testName: string, testFn: () => Promise<boolean>) => {
    try {
      addLog(`Running test: ${testName}`)
      const result = await testFn()
      setTestResults(prev => ({ ...prev, [testName]: result }))
      addLog(`Test ${testName}: ${result ? 'PASS' : 'FAIL'}`)
      return result
    } catch (error) {
      addLog(`Test ${testName}: ERROR - ${error}`)
      setTestResults(prev => ({ ...prev, [testName]: false }))
      return false
    }
  }

  useEffect(() => {
    addLog('Setting up auth listener')
    
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        addLog(`Auth event: ${event}`)
        setSession(session)
        setUser(session?.user ?? null)
        setLoading(false)
      }
    )

    // Get initial session
    supabase.auth.getSession().then(({ data: { session }, error }) => {
      if (error) {
        addLog(`Initial session error: ${error.message}`)
      } else {
        addLog(`Initial session: ${session ? 'Found' : 'None'}`)
      }
      setSession(session)
      setUser(session?.user ?? null)
      setLoading(false)
    })

    return () => {
      addLog('Cleaning up auth listener')
      subscription.unsubscribe()
    }
  }, [])

  const handleSignIn = async () => {
    addLog(`Attempting sign in for: ${email}`)
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password
    })
    
    if (error) {
      addLog(`Sign in error: ${error.message}`)
    } else {
      addLog(`Sign in success: ${data.user?.email}`)
    }
  }

  const handleSignOut = async () => {
    addLog('Attempting sign out')
    const { error } = await supabase.auth.signOut()
    
    if (error) {
      addLog(`Sign out error: ${error.message}`)
    } else {
      addLog('Sign out success')
    }
  }

  const runAllTests = async () => {
    addLog('Starting comprehensive auth tests')
    
    await runTest('Database Connection', async () => {
      const { data, error } = await supabase.from('profiles').select('id').limit(1)
      return !error
    })

    await runTest('Auth Service Available', async () => {
      const { data, error } = await supabase.auth.getSession()
      return error === null
    })

    if (user) {
      await runTest('User Profile Exists', async () => {
        const { data, error } = await supabase
          .from('profiles')
          .select('id')
          .eq('id', user.id)
          .single()
        return !error && data !== null
      })

      await runTest('Notifications Table Access', async () => {
        const { data, error } = await supabase
          .from('notifications')
          .select('id')
          .eq('user_id', user.id)
          .limit(1)
        return error === null
      })

      await runTest('RPC Function Access', async () => {
        const { data, error } = await supabase.rpc('get_unread_notification_count')
        return error === null
      })
    }
  }

  return (
    <div className="min-h-screen bg-background p-6">
      <div className="max-w-4xl mx-auto space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5" />
              Authentication Debug & Test
            </CardTitle>
            <CardDescription>
              Isolated authentication testing to diagnose issues
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Auth Status */}
            <div className="grid md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <h3 className="font-medium">Authentication Status</h3>
                <div className="space-y-1">
                  <div className="flex items-center gap-2">
                    <Badge variant={loading ? 'secondary' : user ? 'default' : 'destructive'}>
                      {loading ? 'Loading...' : user ? 'Authenticated' : 'Not Authenticated'}
                    </Badge>
                  </div>
                  {user && (
                    <div className="text-sm space-y-1">
                      <div>Email: {user.email}</div>
                      <div>ID: {user.id}</div>
                      <div>Created: {new Date(user.created_at).toLocaleString()}</div>
                    </div>
                  )}
                  {session && (
                    <div className="text-sm">
                      <div>Session expires: {new Date(session.expires_at! * 1000).toLocaleString()}</div>
                    </div>
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <h3 className="font-medium">Test Results</h3>
                <div className="space-y-1">
                  {Object.entries(testResults).map(([test, passed]) => (
                    <div key={test} className="flex items-center gap-2 text-sm">
                      {passed ? (
                        <CheckCircle className="h-4 w-4 text-green-500" />
                      ) : (
                        <XCircle className="h-4 w-4 text-red-500" />
                      )}
                      <span>{test}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Sign In Form */}
            {!user && (
              <div className="space-y-4">
                <h3 className="font-medium">Sign In</h3>
                <div className="grid md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="email">Email</Label>
                    <Input
                      id="email"
                      type="email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      placeholder="<EMAIL>"
                    />
                  </div>
                  <div>
                    <Label htmlFor="password">Password</Label>
                    <Input
                      id="password"
                      type="password"
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      placeholder="Your password"
                    />
                  </div>
                </div>
                <Button onClick={handleSignIn} disabled={!email || !password}>
                  Sign In
                </Button>
              </div>
            )}

            {/* Actions */}
            <div className="flex gap-2">
              <Button onClick={runAllTests}>
                Run All Tests
              </Button>
              {user && (
                <Button variant="outline" onClick={handleSignOut}>
                  Sign Out
                </Button>
              )}
              <Button 
                variant="destructive" 
                onClick={() => {
                  localStorage.clear()
                  sessionStorage.clear()
                  window.location.reload()
                }}
              >
                Clear & Reload
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Auth Logs */}
        <Card>
          <CardHeader>
            <CardTitle>Authentication Logs</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="bg-black text-green-400 p-4 rounded font-mono text-sm h-64 overflow-y-auto">
              {authLogs.map((log, index) => (
                <div key={index}>{log}</div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

export default AuthTestPage
