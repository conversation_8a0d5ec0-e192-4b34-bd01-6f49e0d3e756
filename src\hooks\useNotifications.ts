import { useState, useEffect, useCallback } from 'react'
import { NotificationService, Notification, NotificationFilters } from '@/services/notificationService'
import { useAuth } from '@/contexts/AuthContext'

export function useNotifications(filters: NotificationFilters = {}) {
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [unreadCount, setUnreadCount] = useState<number>(0)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const { user } = useAuth()

  // Load notifications
  const loadNotifications = useCallback(async () => {
    // Don't load anything if user is not authenticated or still loading
    if (!user) {
      setNotifications([])
      setUnreadCount(0)
      setLoading(false)
      setError(null)
      return
    }

    try {
      setLoading(true)
      setError(null)

      const [notificationsResult, count] = await Promise.all([
        NotificationService.getUserNotifications(filters),
        NotificationService.getUnreadCount()
      ])

      setNotifications(notificationsResult.notifications)
      setUnreadCount(count)
    } catch (err) {
      console.error('Error loading notifications:', err)
      setError(err instanceof Error ? err.message : 'Failed to load notifications')
      // Reset to empty state on error
      setNotifications([])
      setUnreadCount(0)
    } finally {
      setLoading(false)
    }
  }, [user, filters])

  // Mark notification as read
  const markAsRead = useCallback(async (notificationId: string) => {
    try {
      await NotificationService.markAsRead(notificationId)
      
      // Update local state
      setNotifications(prev => 
        prev.map(n => 
          n.id === notificationId 
            ? { ...n, status: 'read', read_at: new Date().toISOString() }
            : n
        )
      )
      
      // Update unread count
      setUnreadCount(prev => Math.max(0, prev - 1))
    } catch (err) {
      console.error('Error marking notification as read:', err)
      throw err
    }
  }, [])

  // Mark all notifications as read
  const markAllAsRead = useCallback(async () => {
    try {
      const updatedCount = await NotificationService.markAllAsRead()
      
      // Update local state
      setNotifications(prev => 
        prev.map(n => 
          n.status === 'unread' 
            ? { ...n, status: 'read', read_at: new Date().toISOString() }
            : n
        )
      )
      
      setUnreadCount(0)
      return updatedCount
    } catch (err) {
      console.error('Error marking all notifications as read:', err)
      throw err
    }
  }, [])

  // Delete notification
  const deleteNotification = useCallback(async (notificationId: string) => {
    try {
      await NotificationService.deleteNotification(notificationId)
      
      // Update local state
      setNotifications(prev => {
        const notification = prev.find(n => n.id === notificationId)
        const updatedNotifications = prev.filter(n => n.id !== notificationId)
        
        // Update unread count if we deleted an unread notification
        if (notification?.status === 'unread') {
          setUnreadCount(prevCount => Math.max(0, prevCount - 1))
        }
        
        return updatedNotifications
      })
    } catch (err) {
      console.error('Error deleting notification:', err)
      throw err
    }
  }, [])

  // Subscribe to real-time updates
  useEffect(() => {
    if (!user) return

    let unsubscribe: (() => void) | null = null

    const setupSubscription = async () => {
      try {
        unsubscribe = await NotificationService.subscribeToNotifications(
          (notification: Notification) => {
            // Add new notification to the list
            setNotifications(prev => [notification, ...prev])
            
            // Update unread count if it's unread
            if (notification.status === 'unread') {
              setUnreadCount(prev => prev + 1)
            }
          },
          (error) => {
            console.error('Notification subscription error:', error)
          }
        )
      } catch (err) {
        console.error('Failed to set up notification subscription:', err)
      }
    }

    setupSubscription()

    return () => {
      if (unsubscribe) {
        unsubscribe()
      }
    }
  }, [user])

  // Load notifications when user or filters change
  useEffect(() => {
    // Only load if user is authenticated
    if (user) {
      loadNotifications()
    }
  }, [loadNotifications, user])

  return {
    notifications,
    unreadCount,
    loading,
    error,
    refresh: loadNotifications,
    markAsRead,
    markAllAsRead,
    deleteNotification
  }
}

// Hook specifically for unread count (lighter weight)
export function useUnreadNotificationCount() {
  const [unreadCount, setUnreadCount] = useState<number>(0)
  const [loading, setLoading] = useState(true)
  const { user } = useAuth()

  const loadUnreadCount = useCallback(async () => {
    // Don't load anything if user is not authenticated
    if (!user) {
      setUnreadCount(0)
      setLoading(false)
      return
    }

    try {
      setLoading(true)
      const count = await NotificationService.getUnreadCount()
      setUnreadCount(count)
    } catch (err) {
      console.error('Error loading unread count:', err)
      // Reset to 0 on error
      setUnreadCount(0)
    } finally {
      setLoading(false)
    }
  }, [user])

  // Subscribe to real-time updates for count
  useEffect(() => {
    if (!user) return

    let unsubscribe: (() => void) | null = null

    const setupSubscription = async () => {
      try {
        unsubscribe = await NotificationService.subscribeToNotifications(
          (notification: Notification) => {
            if (notification.status === 'unread') {
              setUnreadCount(prev => prev + 1)
            }
          }
        )
      } catch (err) {
        console.error('Failed to set up notification count subscription:', err)
      }
    }

    setupSubscription()

    return () => {
      if (unsubscribe) {
        unsubscribe()
      }
    }
  }, [user])

  useEffect(() => {
    // Only load if user is authenticated
    if (user) {
      loadUnreadCount()
    }
  }, [loadUnreadCount, user])

  return {
    unreadCount,
    loading,
    refresh: loadUnreadCount,
    decrementCount: () => setUnreadCount(prev => Math.max(0, prev - 1)),
    resetCount: () => setUnreadCount(0)
  }
}
