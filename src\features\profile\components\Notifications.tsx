import React, { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { Bell, Check, Trash2, MoreVertical, Filter, Archive, RefreshCw } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger, DropdownMenuCheckboxItem, DropdownMenuSeparator } from '@/components/ui/dropdown-menu'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Ta<PERSON>, Tabs<PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { useToast } from '@/hooks/use-toast'
import { useNotifications } from '@/hooks/useNotifications'
import { NotificationService, Notification, NotificationType, NotificationStatus } from '@/services/notificationService'
import { cn } from '@/lib/utils'

const Notifications: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'all' | 'unread' | 'read'>('all')
  const [selectedTypes, setSelectedTypes] = useState<NotificationType[]>([])
  const { toast } = useToast()
  const navigate = useNavigate()

  // Get filters based on active tab
  const getFilters = () => {
    const filters: any = { limit: 50 }
    
    if (activeTab === 'unread') {
      filters.status = ['unread']
    } else if (activeTab === 'read') {
      filters.status = ['read']
    }
    
    if (selectedTypes.length > 0) {
      filters.type = selectedTypes
    }
    
    return filters
  }

  const { 
    notifications, 
    unreadCount, 
    loading, 
    error,
    refresh,
    markAsRead, 
    markAllAsRead, 
    deleteNotification 
  } = useNotifications(getFilters())

  const handleNotificationClick = async (notification: Notification) => {
    try {
      // Mark as read if unread
      if (notification.status === 'unread') {
        await markAsRead(notification.id)
      }

      // Navigate to action URL if provided
      if (notification.action_url) {
        navigate(notification.action_url)
      }
    } catch (error) {
      console.error('Error handling notification click:', error)
    }
  }

  const handleMarkAllAsRead = async () => {
    try {
      const count = await markAllAsRead()
      toast({
        title: "Success",
        description: `Marked ${count} notifications as read`
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to mark notifications as read",
        variant: "destructive"
      })
    }
  }

  const handleDeleteNotification = async (notificationId: string, e: React.MouseEvent) => {
    e.stopPropagation()
    try {
      await deleteNotification(notificationId)
      toast({
        title: "Success",
        description: "Notification deleted"
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete notification",
        variant: "destructive"
      })
    }
  }

  const toggleTypeFilter = (type: NotificationType) => {
    setSelectedTypes(prev => 
      prev.includes(type) 
        ? prev.filter(t => t !== type)
        : [...prev, type]
    )
  }

  const NotificationItem: React.FC<{ notification: Notification }> = ({ notification }) => (
    <Card 
      className={cn(
        "cursor-pointer hover:bg-gray-50 transition-colors border-l-4",
        notification.status === 'unread' 
          ? "bg-blue-50 border-l-blue-500" 
          : "border-l-gray-200"
      )}
      onClick={() => handleNotificationClick(notification)}
    >
      <CardContent className="p-4">
        <div className="flex items-start justify-between">
          <div className="flex items-start space-x-3 flex-1">
            <div className="flex-shrink-0 mt-1">
              <span className="text-xl" role="img" aria-label={notification.type}>
                {NotificationService.getNotificationIcon(notification.type)}
              </span>
            </div>
            
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2 mb-1">
                <h4 className={cn(
                  "text-sm font-medium text-gray-900",
                  notification.status === 'unread' && "font-semibold"
                )}>
                  {notification.title}
                </h4>
                {notification.status === 'unread' && (
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                )}
              </div>
              
              <p className="text-sm text-gray-600 mb-2">
                {notification.message}
              </p>
              
              <div className="flex items-center gap-3 text-xs text-gray-400">
                <span>{NotificationService.formatNotificationTime(notification.created_at)}</span>
                <Badge variant="outline" className="text-xs">
                  {notification.type.replace('_', ' ')}
                </Badge>
              </div>
            </div>
          </div>
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0"
                onClick={(e) => e.stopPropagation()}
              >
                <MoreVertical className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {notification.status === 'unread' && (
                <DropdownMenuItem
                  onClick={(e) => {
                    e.stopPropagation()
                    markAsRead(notification.id)
                  }}
                >
                  <Check className="h-4 w-4 mr-2" />
                  Mark as read
                </DropdownMenuItem>
              )}
              <DropdownMenuItem
                onClick={(e) => handleDeleteNotification(notification.id, e)}
                className="text-red-600"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardContent>
    </Card>
  )

  const getTabCounts = () => {
    const allCount = notifications.length
    const unreadCountTab = notifications.filter(n => n.status === 'unread').length
    const readCount = notifications.filter(n => n.status === 'read').length
    
    return { all: allCount, unread: unreadCountTab, read: readCount }
  }

  const tabCounts = getTabCounts()

  if (error) {
    return (
      <Card>
        <CardContent className="py-12 text-center">
          <Bell className="h-12 w-12 text-red-300 mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2 text-red-600">Error loading notifications</h3>
          <p className="text-muted-foreground mb-4">{error}</p>
          <Button onClick={refresh} variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            Try again
          </Button>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <Bell className="h-6 w-6" />
            Notifications
          </h2>
          <p className="text-muted-foreground">
            Stay updated with your events and activities
          </p>
        </div>

        <div className="flex items-center gap-2">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">
                <Filter className="h-4 w-4 mr-2" />
                Filter
                {selectedTypes.length > 0 && (
                  <Badge variant="secondary" className="ml-2">
                    {selectedTypes.length}
                  </Badge>
                )}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuCheckboxItem
                checked={selectedTypes.includes('event_registration')}
                onCheckedChange={() => toggleTypeFilter('event_registration')}
              >
                Event Registrations
              </DropdownMenuCheckboxItem>
              <DropdownMenuCheckboxItem
                checked={selectedTypes.includes('event_interest')}
                onCheckedChange={() => toggleTypeFilter('event_interest')}
              >
                Event Interest
              </DropdownMenuCheckboxItem>
              <DropdownMenuCheckboxItem
                checked={selectedTypes.includes('event_update')}
                onCheckedChange={() => toggleTypeFilter('event_update')}
              >
                Event Updates
              </DropdownMenuCheckboxItem>
              <DropdownMenuCheckboxItem
                checked={selectedTypes.includes('event_reminder')}
                onCheckedChange={() => toggleTypeFilter('event_reminder')}
              >
                Event Reminders
              </DropdownMenuCheckboxItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => setSelectedTypes([])}>
                Clear filters
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          {unreadCount > 0 && (
            <Button onClick={handleMarkAllAsRead} size="sm">
              <Check className="h-4 w-4 mr-2" />
              Mark all read
            </Button>
          )}

          <Button variant="outline" size="sm" onClick={refresh}>
            <RefreshCw className="h-4 w-4" />
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as any)}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="all" className="flex items-center gap-2">
            All
            {tabCounts.all > 0 && (
              <Badge variant="secondary" className="text-xs">
                {tabCounts.all}
              </Badge>
            )}
          </TabsTrigger>
          <TabsTrigger value="unread" className="flex items-center gap-2">
            Unread
            {tabCounts.unread > 0 && (
              <Badge variant="destructive" className="text-xs">
                {tabCounts.unread}
              </Badge>
            )}
          </TabsTrigger>
          <TabsTrigger value="read" className="flex items-center gap-2">
            Read
            {tabCounts.read > 0 && (
              <Badge variant="outline" className="text-xs">
                {tabCounts.read}
              </Badge>
            )}
          </TabsTrigger>
        </TabsList>

        <TabsContent value="all" className="mt-6">
          <NotificationList notifications={notifications} loading={loading} />
        </TabsContent>

        <TabsContent value="unread" className="mt-6">
          <NotificationList 
            notifications={notifications.filter(n => n.status === 'unread')} 
            loading={loading} 
          />
        </TabsContent>

        <TabsContent value="read" className="mt-6">
          <NotificationList 
            notifications={notifications.filter(n => n.status === 'read')} 
            loading={loading} 
          />
        </TabsContent>
      </Tabs>
    </div>
  )

  function NotificationList({ notifications: filteredNotifications, loading }: { 
    notifications: Notification[], 
    loading: boolean 
  }) {
    if (loading) {
      return (
        <div className="space-y-4">
          {[...Array(5)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-4">
                <div className="animate-pulse space-y-3">
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-3 bg-gray-200 rounded w-full"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )
    }

    if (filteredNotifications.length === 0) {
      return (
        <Card>
          <CardContent className="py-12 text-center">
            <Bell className="h-12 w-12 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">No notifications</h3>
            <p className="text-muted-foreground">
              {activeTab === 'unread' 
                ? "You're all caught up! No unread notifications."
                : activeTab === 'read'
                ? "No read notifications to show."
                : "You don't have any notifications yet."
              }
            </p>
          </CardContent>
        </Card>
      )
    }

    return (
      <div className="space-y-3">
        {filteredNotifications.map((notification) => (
          <NotificationItem key={notification.id} notification={notification} />
        ))}
      </div>
    )
  }
}

export default Notifications
