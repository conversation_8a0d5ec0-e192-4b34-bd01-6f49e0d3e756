import React from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { useAuth } from '@/contexts/AuthContext'
import { useToast } from '@/hooks/use-toast'
import { RefreshCw, Trash2, User, AlertTriangle } from 'lucide-react'

export const AuthDebugPanel: React.FC = () => {
  const { user, session, clearAuthState, loading } = useAuth()
  const { toast } = useToast()

  const handleClearAuth = async () => {
    try {
      await clearAuthState()
      toast({
        title: "Auth State Cleared",
        description: "Authentication state has been cleared. Please refresh and log in again.",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to clear auth state",
        variant: "destructive"
      })
    }
  }

  const handleClearStorage = () => {
    localStorage.clear()
    sessionStorage.clear()
    toast({
      title: "Storage Cleared",
      description: "Browser storage cleared. Please refresh the page.",
    })
  }

  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <AlertTriangle className="h-5 w-5 text-amber-500" />
          Auth Debug Panel
        </CardTitle>
        <CardDescription>
          Debug authentication issues and clear invalid sessions
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Auth Status */}
        <div className="space-y-2">
          <h4 className="font-medium">Authentication Status</h4>
          <div className="flex items-center gap-2">
            <User className="h-4 w-4" />
            <Badge variant={user ? 'default' : 'secondary'}>
              {loading ? 'Loading...' : user ? 'Authenticated' : 'Not Authenticated'}
            </Badge>
          </div>
          {user && (
            <div className="text-sm text-muted-foreground">
              <div>Email: {user.email}</div>
              <div>ID: {user.id.slice(0, 8)}...</div>
            </div>
          )}
          {session && (
            <div className="text-sm text-muted-foreground">
              Session expires: {new Date(session.expires_at! * 1000).toLocaleString()}
            </div>
          )}
        </div>

        {/* Actions */}
        <div className="space-y-2">
          <h4 className="font-medium">Actions</h4>
          
          <Button 
            variant="outline" 
            size="sm" 
            onClick={handleClearAuth}
            className="w-full"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Clear Auth State
          </Button>
          
          <Button 
            variant="outline" 
            size="sm" 
            onClick={handleClearStorage}
            className="w-full"
          >
            <Trash2 className="h-4 w-4 mr-2" />
            Clear Browser Storage
          </Button>
        </div>

        {/* Instructions */}
        <div className="p-3 bg-amber-50 border border-amber-200 rounded-lg">
          <h4 className="font-medium text-amber-900 text-sm">Fix Auth Issues:</h4>
          <ol className="text-xs text-amber-800 mt-1 space-y-1">
            <li>1. Click "Clear Auth State" or "Clear Browser Storage"</li>
            <li>2. Refresh the page (F5)</li>
            <li>3. Log in again</li>
          </ol>
        </div>
      </CardContent>
    </Card>
  )
}
